package com.dahua.demo.basic.video;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;
import com.google.gson.Gson;

import java.util.Map;

/**
 * 获取实时监视URI
 *  注意：使用前请根据实际情况修改请求的参数，修改完后调用main方法即可
 * 		 请修改 content 中的 channelId 和 scheme 参数
 * 		 本demo的content中，只是给出了一个例子，可以根据参数选填的情况，自由组合content中参数内容
 */
class RealMonitor extends BaseUserInfo
{
	public static final String ACTION = "/videoService/realmonitor/uri";

	//获取实时监视URI
	private static String getRealMonitor(String ip, int port, String token) throws Exception{
		/**
		 * channelId : 类型string ，必填。通道编码。channelID为之前调用分级获取设备通道的channelid，scheme为获取视频类型
		 * subType : 类型int ，选填。码流类型，0:主码流、1:辅流1、2:辅流2。默认为0主码流。
		 * scheme : 类型string ，选填。协议类型，支持RTSP、FLV_HTTP、HLS三种，默认RTSP。
		 */
		String channelId="sZkZQKyWA1BE7UL5PDT23B";
		String scheme="RTSP";
		String content = "?channelId=" +channelId+
				         "&scheme="+scheme;
		String response=HttpTestUtils.httpRequest(HttpEnum.GET,ip,port,ACTION,token,content);
		Map<String, Object> rsp = new Gson().fromJson(response, Map.class);
		Object url = rsp.get("url");
		System.out.println("url:  "+url);

		return response;
	}

	public static void main(String[] args) throws Exception {
		String rsp = getRealMonitor(ip, Integer.valueOf(port), token);
	}
}



