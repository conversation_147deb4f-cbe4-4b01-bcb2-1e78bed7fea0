﻿#接口名称和接口对应类关系表

#会话管理
创建会话=Login
会话保活=KeepLogin
销毁会话=LoginOut
#基础功能
分级获取组织=OrgTree
分级获取设备通道=DevInfo
获取实时监控URI=RealMonitor
录像查询=Record
获取回放URI=Playback
云台转动=Ptz
获取预置点=GetPreset
转动到预置点=PtzGoto
设置预置点=SetPreset
清除预置点=DeletePreset
消息订阅=SubscribeAddress
长轮询获取消息=Subscribe
#车辆智能
过车查询=QueryVehicle
违章查询=QueryIllegal
违章详情=IllegalDetail
新增布控=AddSurveillance
审核布控=AuditSurveillance
撤销布控=RevokeSurveillance
查询布控=QuerySurveillance
布控详情=SurveillanceDetail
查询报警=AddRepository
报警详情=AlarmDetail
查询二次分析=QueryPasCarAnalyse
品牌字典获取=GetBrandDict
车系年款字典获取=GetCarDetail

#人像
人像库新增=AddRepo
人像库修改=ModifyRepo
人像库删除=DeleteRepo
人像库查询 (GET)=GetRepo
人像库详情=RepoDetail
库成员新增=AddMember
库成员修改=ModifyMember
库成员删除=DeleteMember
库成员详情=MemberDetail
布控设置=SetSurveillance
布控批量设置=SetSurveillanceBatch
布控删除=DeleteSurveillance
布控批量删除=DeleteSurveillanceBatch
布控查询=QuerySurveillance
报警记录查询=QueryAlarm
报警记录详情=AlarmDetail
抓拍库查询和检索=QueryCapture
抓拍记录详情=CaptureDetail
人像库查询和检索=QueryRepo
报警总数统计=AlarmCountStatistics
报警总数查询=AlarmCountQuery
抓拍总数统计=CaptureCountStatistics
抓拍总数查询=CaptureCountQuery
人像库总数统计=RepoTotal





