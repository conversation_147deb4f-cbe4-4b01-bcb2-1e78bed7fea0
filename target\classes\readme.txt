1, 本项目为maven项目，每个接口调试为单独的main方法调用，可以单独执行，本项目中的参数为举例，可根据需求自己修改参数，进行接口测试
2，每个接口调用实例中只是一种情况，仅提供了部分参数，可以参考《大华视频云_合作开发指南.pdf》中对应接口的参数说明来调用，且以《大华视频云_合作开发指南.pdf》中的说明为准
3，使用方法：
   3.1 首先修改baseinfo.properties中的ip，port，username，password为对接的内容
   3.2 本demo模拟实际场景中使用情况，调用接口前必须调用Login进行登录，KeepLogin进行登录保活，否则会弹出未登录提示
       且KeepLogin该main方法必须在执行状态，不能关闭该方法（登录保活时间为110s）,接口调用完成后调用LogOut进行登出
   3.3 然后从nametointerface.properties找到对应的类名，直接打开类名，调用main方法即可
   3.4 每个接口调用实例中 有两个地方可以修改
       3.4.1 ACTION对应的常量为请求的url，部分接口的url中拼接了参数，需要修改
       3.4.2 content中为get，delete中url中拼接的参数，put，post请求体中的json内容。后端做了处理，只需要把内容填入即可
4，接口调用异常时
   首先查看参数内容是否符合《大华视频云_合作开发指南.pdf》的要求
5,具体的业务流程请查看http://open.dahuatech.com  --》接口文档--》开发指南--》业务介绍  中的业务流程图，按照其顺序调用本项目接口即可实现功能


附录：功能和具体包的对应关系
#会话和基础视频相关接口
basic.preset           预置点相关接口
login                  登陆，保活，登出接口
basic.subscribe        订阅相关接口
basic.video            视屏基础功能相关接口

#车辆相关接口
vehicle.alarm          车辆报警相关接口
vehicle.caranalyse     车辆二次分析接口
vehicle.dict           车辆字典查询接口
vehicle.pass           车辆通行与违章接口
vehicle.surveillance   车辆布控相关接口

#人像相关接口
faceService.repository   人像库相关接口
faceService.repomember   人像库成员相关接口
faceService.capture      人像抓拍相关接口
faceService.surveillance 人像布控相关接口
faceService.total        人像数据数目统计
